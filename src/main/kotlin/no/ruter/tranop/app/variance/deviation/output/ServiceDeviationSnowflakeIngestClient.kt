package no.ruter.tranop.app.variance.deviation.output

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.dataflow.snowflake.AbstractSnowflakeIngestClient
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeClientFactory
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeClientProperties
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviation
import no.ruter.tranop.operation.deviation.bi.model.BIServiceDeviation
import org.springframework.stereotype.Service
import java.time.Instant.now

/**
 * Client for streaming ingestion of service deviations into Snowflake.
 *
 * @property config Configuration for streaming ingest.
 * @property clientFactory Factory for creating Snowflake clients and channels.
 */
@Service
class ServiceDeviationSnowflakeIngestClient(
    config: SnowflakeClientProperties,
    clientFactory: SnowflakeClientFactory,
) : AbstractSnowflakeIngestClient<BIServiceDeviation>(
    config,
    clientFactory,
    CLIENT_BUILDER_NAME,
    CHANNEL_BUILDER_NAME,
) {
    companion object {
        const val CLIENT_BUILDER_NAME = "ASSIGNMENT_DJV2_DEV_SERVICE_DEVIATIONS"
        const val CHANNEL_BUILDER_NAME = "ASSIGNMENT_DJV2_DEV_SERVICE_DEVIATION_CHANNEL"
    }

    override fun BIServiceDeviation.toIngestMap(): Map<String, Any> =
        mapOf(
            "REF" to ref,
            "CREATED_AT" to createdAt,
            "CODE" to (code ?: ""),
            "REASON" to (reason ?: ""),
            "COMMENT" to (comment ?: ""),
            "METADATA" to JsonUtils.toJson(metadata),
        )
}

/**
 * Converts a DTOServiceDeviation to a BIServiceDeviation for Snowflake ingestion.
 */
fun DTOServiceDeviation.toBIServiceDeviation(): BIServiceDeviation =
    BIServiceDeviation(
        ref = ref ?: "",
        createdAt = now().toString(),
        code = spec?.code?.value,
        reason = spec?.reason?.code,
        comment = spec?.reason?.comment,
        metadata = buildMap {
            spec?.duration?.let { duration ->
                put("duration_start", duration.start?.toString())
                put("duration_end", duration.end?.toString())
            }
            spec?.impact?.let { impact ->
                put("impact_lines", impact.lines?.size ?: 0)
                put("impact_journeys", impact.journeys?.size ?: 0)
                put("impact_stop_points", impact.stopPoints?.size ?: 0)
            }
            spec?.metadata?.forEach { metadataEntry ->
                metadataEntry.key?.value?.let { key ->
                    put("metadata_$key", metadataEntry.value)
                }
            }
        },
    )
