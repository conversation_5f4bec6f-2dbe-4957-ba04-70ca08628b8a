package no.ruter.tranop.app.variance.deviation.output.model

/**
 * Business Intelligence model for service deviations.
 * This model represents service deviation data optimized for Snowflake ingestion.
 *
 * @property ref The unique reference identifier for the service deviation
 * @property createdAt The timestamp when the service deviation was created
 * @property code The service deviation code (e.g., DELAY, BYPASS, NO_SERVICE, NO_SIGN_ON)
 * @property reason The reason for the service deviation
 * @property comment Additional comment about the service deviation
 * @property metadata Additional metadata associated with the service deviation
 */
data class BIServiceDeviation(
    val ref: String,
    val createdAt: String,
    val code: String?,
    val reason: String?,
    val comment: String?,
    val metadata: Map<String, Any?>,
)
