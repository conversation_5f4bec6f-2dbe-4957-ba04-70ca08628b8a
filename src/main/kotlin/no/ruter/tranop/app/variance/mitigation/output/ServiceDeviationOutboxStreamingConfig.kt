package no.ruter.tranop.app.variance.deviation.output

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = ServiceDeviationOutboxStreamingConfig.CONF_PREFIX)
class ServiceDeviationOutboxStreamingConfig {
    var batchSize = 1000
    var enabled = false
    var retryCount = 5

    companion object {
        const val CONF_PREFIX = "app.config.outbox.snowflake-streaming-service-deviation"
    }
}
