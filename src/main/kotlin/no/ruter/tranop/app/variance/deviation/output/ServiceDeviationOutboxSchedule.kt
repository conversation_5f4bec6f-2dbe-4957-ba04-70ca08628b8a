package no.ruter.tranop.app.variance.deviation.output

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import no.ruter.rdp.common.json.JsonUtils
import no.ruter.rdp.logging.LoggerFactory
import no.ruter.tranop.app.common.db.record.AbstractQueryBuilder
import no.ruter.tranop.app.common.time.TimeService
import no.ruter.tranop.app.outbox.OutboxService
import no.ruter.tranop.operation.deviation.bi.model.BIServiceDeviation
import no.ruter.tranop.outbox.db.model.DBOutboxDataType
import no.ruter.tranop.outbox.db.model.DBOutboxTargetType
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(
    prefix = ServiceDeviationOutboxStreamingConfig.CONF_PREFIX,
    name = ["enabled"],
    havingValue = "true",
)
class ServiceDeviationOutboxSchedule(
    val timeService: TimeService,
    val outboxService: OutboxService,
    val streamingIngestClient: ServiceDeviationSnowflakeIngestClient,
    val config: ServiceDeviationOutboxStreamingConfig,
) {
    companion object {
        const val FREQ_PREFIX = "${ServiceDeviationOutboxStreamingConfig.CONF_PREFIX}.frequent"
    }

    private val log = LoggerFactory.getLogger(javaClass.canonicalName)

    @Scheduled(
        fixedRateString = "\${${FREQ_PREFIX}.fixedRate}",
        initialDelayString = "\${${FREQ_PREFIX}.initialDelay}",
    )
    @SchedulerLock(
        name = "\${${FREQ_PREFIX}.schedulerLockName}",
        lockAtMostFor = "\${${FREQ_PREFIX}.lockAtMostFor}",
        lockAtLeastFor = "\${${FREQ_PREFIX}.lockAtLeastFor}",
    )
    fun processPendingEvents() {
        val unpublished =
            outboxService.findUnpublished(
                DBOutboxDataType.SERVICE_DEVIATION,
                DBOutboxTargetType.SNOWFLAKE_JSON_BI_V1,
                AbstractQueryBuilder.Pagination(config.batchSize, 0),
                config.retryCount,
            )
        runCatching {
            val (successFullIngestEvents, failedIngestEvents) =
                streamingIngestClient.ingest(
                    unpublished.map {
                        Pair(
                            it.record.ref,
                            JsonUtils.toObject(it.data.payload, BIServiceDeviation::class.java)
                        )
                    },
                )
            log.debug("Successfully ingested ${successFullIngestEvents.size} service deviations, failed to ingest ${failedIngestEvents.size} service deviations.")

            if (successFullIngestEvents.isNotEmpty()) {
                outboxService.markAsPublished(successFullIngestEvents)
            }
            if (failedIngestEvents.isNotEmpty()) {
                outboxService.markAsFailed(
                    unpublished.filter { it ->
                        failedIngestEvents.contains(it.record.ref)
                    },
                )
            }
        }.onFailure { e ->
            log.error("Failed to process pending service deviation events, marking as failed", e)
            outboxService.markAsFailed(unpublished)
        }
    }
}
