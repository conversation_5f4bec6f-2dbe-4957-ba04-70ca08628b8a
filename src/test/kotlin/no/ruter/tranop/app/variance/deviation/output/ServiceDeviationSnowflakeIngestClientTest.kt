package no.ruter.tranop.app.variance.deviation.output

import no.ruter.rdp.common.json.JsonUtils
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeTestClientFactory
import no.ruter.tranop.app.common.dataflow.snowflake.SnowflakeTestConfig
import no.ruter.tranop.app.variance.deviation.output.model.BIServiceDeviation
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviation
import no.ruter.tranop.journey.deviation.dto.model.DTOServiceDeviationSpec
import no.ruter.tranop.journey.deviation.dto.model.common.DTOServiceDeviationReason
import no.ruter.tranop.journey.deviation.dto.value.DTOServiceDeviationCode
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

class ServiceDeviationSnowflakeIngestClientTest {
    val clientFactory = SnowflakeTestClientFactory()

    val streamingIngestClient =
        ServiceDeviationSnowflakeIngestClient(
            config = SnowflakeTestConfig.DEFAULT_CLIENT_PROPERTIES,
            clientFactory = clientFactory,
        )

    @BeforeEach
    fun setUp() {
        clientFactory.reset()
    }

    @Test
    fun `stream sends service deviation data to Snowflake successfully`() {
        val serviceDeviation = DTOServiceDeviation().apply {
            this.ref = "sd-test-123"
            this.spec = DTOServiceDeviationSpec().apply {
                this.code = DTOServiceDeviationCode.DELAY
                this.reason = DTOServiceDeviationReason().apply {
                    this.code = "TRAFFIC_CONGESTION"
                    this.comment = "Heavy traffic causing delays"
                }
            }
        }

        val biServiceDeviation = serviceDeviation.toBIServiceDeviation()
        streamingIngestClient.ingest(listOf(Pair("outbox-ref-1", biServiceDeviation)))

        val capturedMap = firstIngestedRow()
        assertEquals("sd-test-123", capturedMap["REF"])
        assertNotNull(capturedMap["CREATED_AT"])
        assertEquals("DELAY", capturedMap["CODE"])
        assertEquals("TRAFFIC_CONGESTION", capturedMap["REASON"])
        assertEquals("Heavy traffic causing delays", capturedMap["COMMENT"])
        
        val metadataString = capturedMap["METADATA"].toString()
        val metadata = JsonUtils.toJsonNode(metadataString)
        assertNotNull(metadata)
    }

    @Test
    fun `stream sends service deviation with null values successfully`() {
        val serviceDeviation = DTOServiceDeviation().apply {
            this.ref = "sd-test-456"
            this.spec = DTOServiceDeviationSpec().apply {
                this.code = DTOServiceDeviationCode.NO_SERVICE
                // reason is null
            }
        }

        val biServiceDeviation = serviceDeviation.toBIServiceDeviation()
        streamingIngestClient.ingest(listOf(Pair("outbox-ref-2", biServiceDeviation)))

        val capturedMap = firstIngestedRow()
        assertEquals("sd-test-456", capturedMap["REF"])
        assertNotNull(capturedMap["CREATED_AT"])
        assertEquals("NO_SERVICE", capturedMap["CODE"])
        assertEquals("", capturedMap["REASON"]) // Should be empty string for null
        assertEquals("", capturedMap["COMMENT"]) // Should be empty string for null
    }

    @Test
    fun `toBIServiceDeviation converts DTOServiceDeviation correctly`() {
        val serviceDeviation = DTOServiceDeviation().apply {
            this.ref = "sd-conversion-test"
            this.spec = DTOServiceDeviationSpec().apply {
                this.code = DTOServiceDeviationCode.BYPASS
                this.reason = DTOServiceDeviationReason().apply {
                    this.code = "CONSTRUCTION"
                    this.comment = "Road construction work"
                }
            }
        }

        val biServiceDeviation = serviceDeviation.toBIServiceDeviation()

        assertEquals("sd-conversion-test", biServiceDeviation.ref)
        assertEquals("BYPASS", biServiceDeviation.code)
        assertEquals("CONSTRUCTION", biServiceDeviation.reason)
        assertEquals("Road construction work", biServiceDeviation.comment)
        assertNotNull(biServiceDeviation.createdAt)
        assertNotNull(biServiceDeviation.metadata)
    }

    private fun firstIngestedRow(): Map<String?, Any?> =
        clientFactory
            .channel(
                clientName = ServiceDeviationSnowflakeIngestClient.CLIENT_BUILDER_NAME,
                channelName = ServiceDeviationSnowflakeIngestClient.CHANNEL_BUILDER_NAME,
            ).rows
            .first() ?: throw AssertionError("ingested row is null")
}
